import type { Context } from "hono";
import { getDb } from "@/database";
import { logInfo } from "@/utils/logger";
import { addWebhookToQueue, extractSourceInfo } from "@/utils/webhookQueue";

export async function queueCCWebhookHandler(c: Context): Promise<Response> {
  const timestamp = new Date().toISOString();
  const baseUrl = new URL(c.req.url).origin;
  let res;

  try {
    logInfo("Received CC webhook for queue");

    const payload = await c.req.json();

    const db = getDb();

    // Validate required fields
    if (!payload.id) {
      throw new Error("CC webhook payload missing required 'id' field");
    }

    // Extract source information and create webhook data
    const sourceInfo = extractSourceInfo(payload);
    const webhookId = crypto.randomUUID();
    const now = new Date();

    const webhookData = {
      id: webhookId,
      source: sourceInfo.source,
      sourceId: sourceInfo.sourceId,
      type: sourceInfo.type as "patient" | "appointment",
      payload: payload,
      status: "pending" as const,
      retryCount: 0,
      maxRetries: 3,
      createdAt: now,
      updatedAt: now,
    };

    // Add webhook to queue with duplicate prevention
    const createdWebhook = await addWebhookToQueue(db, webhookData);

    logInfo(`CC webhook stored in queue: ${createdWebhook.id}`);

    res = c.json(
      {
        status: "success",
        message: "CC webhook stored in queue",
        metadata: {
          timestamp,
          webhookId: createdWebhook.id,
        },
      },
      200
    );
  } catch (_error) {
    res = c.json(
      {
        status: "error",
        message: "Failed to store CC webhook in queue",
        metadata: {
          timestamp,
        },
      },
      500
    );
  }
  fetch(`${baseUrl}/webhook-trigger`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "User-Agent": "WebhookTrigger-Chain/1.0",
    },
  });
  return res;
}

export async function queueAPWebhookHandler(c: Context): Promise<Response> {
  const timestamp = new Date().toISOString();
  const baseUrl = new URL(c.req.url).origin;
  let res;

  try {
    logInfo("Received AP webhook for queue");

    const payload = await c.req.json();

    const db = getDb();

    // Validate required fields
    if (!payload.contact_id) {
      throw new Error("AP webhook payload missing required 'contact_id' field");
    }

    // Extract source information and create webhook data
    const sourceInfo = extractSourceInfo(payload);
    const webhookId = crypto.randomUUID();
    const now = new Date();

    const webhookData = {
      id: webhookId,
      source: sourceInfo.source,
      sourceId: sourceInfo.sourceId,
      type: sourceInfo.type as "patient" | "appointment",
      payload: payload,
      status: "pending" as const,
      retryCount: 0,
      maxRetries: 3,
      createdAt: now,
      updatedAt: now,
    };

    // Add webhook to queue with duplicate prevention
    const createdWebhook = await addWebhookToQueue(db, webhookData);

    logInfo(`AP webhook stored in queue: ${createdWebhook.id}`);

    res = c.json(
      {
        status: "success",
        message: "AP webhook stored in queue",
        metadata: {
          timestamp,
          webhookId: createdWebhook.id,
        },
      },
      200
    );
  } catch (_error) {
    console.log(_error);
    res = c.json(
      {
        status: "error",
        message: "Failed to store AP webhook in queue",
        metadata: {
          timestamp,
        },
      },
      500
    );
  }
  fetch(`${baseUrl}/webhook-trigger`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "User-Agent": "WebhookTrigger-Chain/1.0",
    },
  });
  logInfo("Webhook trigger called");
  return res;
}
