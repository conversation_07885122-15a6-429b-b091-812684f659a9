CREATE TABLE "webhook_queue" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"source" text NOT NULL,
	"source_id" text NOT NULL,
	"type" varchar(255) DEFAULT 'patient' NOT NULL,
	"payload" jsonb NOT NULL,
	"status" varchar(255) DEFAULT 'pending' NOT NULL,
	"retry_count" integer DEFAULT 0 NOT NULL,
	"last_retry_attempt_at" timestamp,
	"last_retry_reason" text,
	"max_retries" integer DEFAULT 3 NOT NULL,
	"processing_started_at" timestamp,
	"processing_completed_at" timestamp,
	"error_message" text,
	"error_details" jsonb
);
