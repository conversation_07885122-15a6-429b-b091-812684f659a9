{"id": "e2253a9f-16bf-4e93-b5ad-800c1a476bf6", "prevId": "4ddab029-e000-47bf-8995-362f3e00a7be", "version": "7", "dialect": "postgresql", "tables": {"public.appointments": {"name": "appointments", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "ap_id": {"name": "ap_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "cc_id": {"name": "cc_id", "type": "integer", "primaryKey": false, "notNull": false}, "patient_id": {"name": "patient_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "ap_updated_at": {"name": "ap_updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "cc_updated_at": {"name": "cc_updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "ap_data": {"name": "ap_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "cc_data": {"name": "cc_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "ap_note_id": {"name": "ap_note_id", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"appointments_patient_id_patients_id_fk": {"name": "appointments_patient_id_patients_id_fk", "tableFrom": "appointments", "tableTo": "patients", "columnsFrom": ["patient_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"appointments_ap_id_unique": {"name": "appointments_ap_id_unique", "nullsNotDistinct": false, "columns": ["ap_id"]}, "appointments_cc_id_unique": {"name": "appointments_cc_id_unique", "nullsNotDistinct": false, "columns": ["cc_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.custom_fields": {"name": "custom_fields", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "ap_id": {"name": "ap_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "cc_id": {"name": "cc_id", "type": "integer", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "label": {"name": "label", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "ap_config": {"name": "ap_config", "type": "jsonb", "primaryKey": false, "notNull": false}, "cc_config": {"name": "cc_config", "type": "jsonb", "primaryKey": false, "notNull": false}, "mapping_type": {"name": "mapping_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'custom_to_custom'"}, "ap_standard_field": {"name": "ap_standard_field", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "cc_standard_field": {"name": "cc_standard_field", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"custom_fields_ap_id_unique": {"name": "custom_fields_ap_id_unique", "nullsNotDistinct": false, "columns": ["ap_id"]}, "custom_fields_cc_id_unique": {"name": "custom_fields_cc_id_unique", "nullsNotDistinct": false, "columns": ["cc_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.error_logs": {"name": "error_logs", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "request_id": {"name": "request_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "stack": {"name": "stack", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.patients": {"name": "patients", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "ap_id": {"name": "ap_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "cc_id": {"name": "cc_id", "type": "integer", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "ap_updated_at": {"name": "ap_updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "cc_updated_at": {"name": "cc_updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "ap_data": {"name": "ap_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "cc_data": {"name": "cc_data", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}