CREATE TABLE "skip" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"patient_id" varchar(255),
	"appointment_id" varchar(255),
	"ap_id" varchar(255),
	"cc_id" integer,
	"email" varchar(255),
	"phone" varchar(255),
	"slot" varchar(255),
	"patients" jsonb
);
--> statement-breakpoint
ALTER TABLE "skip" ADD CONSTRAINT "skip_patient_id_patients_id_fk" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "skip" ADD CONSTRAINT "skip_appointment_id_appointments_id_fk" FOREIGN KEY ("appointment_id") REFERENCES "public"."appointments"("id") ON DELETE no action ON UPDATE no action;